// Simple Node.js server for handling thumbnail uploads
// This is a demonstration of how to implement the server-side thumbnail saving

const express = require('express');
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const cors = require('cors');

const app = express();
const PORT = 3000;

// Enable CORS for all routes
app.use(cors());
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Serve static files from the project directory
app.use(express.static(path.join(__dirname, '..')));

// Configure multer for file uploads
const storage = multer.diskStorage({
    destination: function (req, file, cb) {
        const thumbnailsDir = path.join(__dirname, '..', 'categories', 'Thumbnails');
        
        // Create directory if it doesn't exist
        if (!fs.existsSync(thumbnailsDir)) {
            fs.mkdirSync(thumbnailsDir, { recursive: true });
        }
        
        cb(null, thumbnailsDir);
    },
    filename: function (req, file, cb) {
        // Use the original filename or generate one
        const filename = req.body.filename || file.originalname || `thumbnail_${Date.now()}.jpg`;
        cb(null, filename);
    }
});

const upload = multer({ 
    storage: storage,
    limits: {
        fileSize: 5 * 1024 * 1024 // 5MB limit
    },
    fileFilter: function (req, file, cb) {
        // Accept only image files
        if (file.mimetype.startsWith('image/')) {
            cb(null, true);
        } else {
            cb(new Error('Only image files are allowed!'), false);
        }
    }
});

// API endpoint to save thumbnail file
app.post('/api/save-thumbnail', upload.single('thumbnail'), (req, res) => {
    try {
        if (!req.file) {
            return res.status(400).json({
                success: false,
                error: 'No thumbnail file provided'
            });
        }
        
        const thumbnailPath = `/categories/Thumbnails/${req.file.filename}`;
        
        console.log(`✅ Thumbnail saved: ${req.file.filename} (${req.file.size} bytes)`);
        
        res.json({
            success: true,
            path: thumbnailPath,
            url: thumbnailPath,
            filename: req.file.filename,
            size: req.file.size,
            message: 'Thumbnail saved successfully'
        });
        
    } catch (error) {
        console.error('Error saving thumbnail:', error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// API endpoint to save thumbnail from base64
app.post('/api/save-thumbnail-base64', (req, res) => {
    try {
        const { videoId, filename, data, folder } = req.body;
        
        if (!data || !filename) {
            return res.status(400).json({
                success: false,
                error: 'Missing data or filename'
            });
        }
        
        // Extract base64 data (remove data:image/jpeg;base64, prefix)
        const base64Data = data.replace(/^data:image\/[a-z]+;base64,/, '');
        
        // Create thumbnails directory if it doesn't exist
        const thumbnailsDir = path.join(__dirname, '..', 'categories', folder || 'Thumbnails');
        if (!fs.existsSync(thumbnailsDir)) {
            fs.mkdirSync(thumbnailsDir, { recursive: true });
        }
        
        // Save file
        const filePath = path.join(thumbnailsDir, filename);
        fs.writeFileSync(filePath, base64Data, 'base64');
        
        const thumbnailPath = `/categories/${folder || 'Thumbnails'}/${filename}`;
        
        console.log(`✅ Base64 thumbnail saved: ${filename}`);
        
        res.json({
            success: true,
            path: thumbnailPath,
            url: thumbnailPath,
            filename: filename,
            message: 'Base64 thumbnail saved successfully'
        });
        
    } catch (error) {
        console.error('Error saving base64 thumbnail:', error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// API endpoint to delete thumbnail
app.delete('/api/delete-thumbnail', (req, res) => {
    try {
        const { path: thumbnailPath } = req.body;
        
        if (!thumbnailPath) {
            return res.status(400).json({
                success: false,
                error: 'No thumbnail path provided'
            });
        }
        
        // Convert URL path to file system path
        const filePath = path.join(__dirname, '..', thumbnailPath);
        
        if (fs.existsSync(filePath)) {
            fs.unlinkSync(filePath);
            console.log(`🗑️ Thumbnail deleted: ${thumbnailPath}`);
            
            res.json({
                success: true,
                message: 'Thumbnail deleted successfully'
            });
        } else {
            res.status(404).json({
                success: false,
                error: 'Thumbnail file not found'
            });
        }
        
    } catch (error) {
        console.error('Error deleting thumbnail:', error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// API endpoint to get thumbnail info
app.post('/api/thumbnail-info', (req, res) => {
    try {
        const { path: thumbnailPath } = req.body;
        
        if (!thumbnailPath) {
            return res.status(400).json({
                success: false,
                error: 'No thumbnail path provided'
            });
        }
        
        const filePath = path.join(__dirname, '..', thumbnailPath);
        
        if (fs.existsSync(filePath)) {
            const stats = fs.statSync(filePath);
            
            res.json({
                success: true,
                path: thumbnailPath,
                size: stats.size,
                created: stats.birthtime,
                modified: stats.mtime,
                exists: true
            });
        } else {
            res.json({
                success: true,
                path: thumbnailPath,
                exists: false
            });
        }
        
    } catch (error) {
        console.error('Error getting thumbnail info:', error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// API endpoint to cleanup old thumbnails
app.post('/api/cleanup-thumbnails', (req, res) => {
    try {
        const { activeVideoIds } = req.body;
        
        const thumbnailsDir = path.join(__dirname, '..', 'categories', 'Thumbnails');
        
        if (!fs.existsSync(thumbnailsDir)) {
            return res.json({
                success: true,
                message: 'Thumbnails directory does not exist',
                deleted: 0
            });
        }
        
        const files = fs.readdirSync(thumbnailsDir);
        let deletedCount = 0;
        
        files.forEach(file => {
            const filePath = path.join(thumbnailsDir, file);
            const stats = fs.statSync(filePath);
            
            // Delete files older than 30 days that don't correspond to active videos
            const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
            
            if (stats.mtime < thirtyDaysAgo) {
                // Check if this thumbnail corresponds to an active video
                const isActive = activeVideoIds.some(id => file.includes(id));
                
                if (!isActive) {
                    fs.unlinkSync(filePath);
                    deletedCount++;
                    console.log(`🗑️ Cleaned up old thumbnail: ${file}`);
                }
            }
        });
        
        res.json({
            success: true,
            message: `Cleanup completed. Deleted ${deletedCount} old thumbnails.`,
            deleted: deletedCount
        });
        
    } catch (error) {
        console.error('Error during thumbnail cleanup:', error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// Start server
app.listen(PORT, () => {
    console.log(`🚀 Thumbnail server running on http://localhost:${PORT}`);
    console.log(`📁 Serving files from: ${path.join(__dirname, '..')}`);
    console.log(`🖼️ Thumbnails will be saved to: ${path.join(__dirname, '..', 'categories', 'Thumbnails')}`);
});

module.exports = app;
