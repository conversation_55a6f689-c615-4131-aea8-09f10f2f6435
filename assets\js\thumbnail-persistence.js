// Thumbnail Persistence System
// This system handles saving generated thumbnails as actual files to the server
class ThumbnailPersistence {
    constructor() {
        this.serverEndpoint = '/api/save-thumbnail'; // Server endpoint for saving thumbnails
        this.thumbnailsFolder = '/categories/Thumbnails/';
        this.maxFileSize = 5 * 1024 * 1024; // 5MB max file size
        this.allowedTypes = ['image/jpeg', 'image/png', 'image/webp'];
    }
    
    // Save thumbnail blob as actual file to server
    async saveThumbnailFile(video, blob, filename = null) {
        try {
            // Generate filename if not provided
            if (!filename) {
                const videoFileName = video.videoUrl.split('/').pop();
                filename = videoFileName.replace(/\.(ts|mp4|webm|avi|mov|mkv|flv)$/i, '.jpg');
            }
            
            // Validate blob
            if (!this.validateBlob(blob)) {
                throw new Error('Invalid blob for thumbnail');
            }
            
            // Create FormData for file upload
            const formData = new FormData();
            formData.append('thumbnail', blob, filename);
            formData.append('videoId', video.id);
            formData.append('videoTitle', video.title);
            formData.append('folder', 'Thumbnails');
            
            // In a real implementation, this would send to server
            // For now, we'll simulate the server response
            const result = await this.simulateServerSave(formData, filename);
            
            if (result.success) {
                console.log(`✅ Thumbnail saved: ${result.path}`);
                return {
                    success: true,
                    path: result.path,
                    url: result.url,
                    filename: filename,
                    size: blob.size
                };
            } else {
                throw new Error(result.error || 'Failed to save thumbnail');
            }
            
        } catch (error) {
            console.error('Error saving thumbnail:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }
    
    // Validate blob before saving
    validateBlob(blob) {
        if (!blob || !(blob instanceof Blob)) {
            return false;
        }
        
        if (blob.size === 0 || blob.size > this.maxFileSize) {
            return false;
        }
        
        if (!this.allowedTypes.includes(blob.type)) {
            return false;
        }
        
        return true;
    }
    
    // Simulate server save operation (replace with actual server call)
    async simulateServerSave(formData, filename) {
        return new Promise((resolve) => {
            // Simulate network delay
            setTimeout(() => {
                // In a real implementation, this would be an actual fetch call:
                /*
                fetch(this.serverEndpoint, {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => resolve(data))
                .catch(error => resolve({ success: false, error: error.message }));
                */
                
                // Simulated successful response
                const path = `${this.thumbnailsFolder}${filename}`;
                resolve({
                    success: true,
                    path: path,
                    url: path,
                    message: 'Thumbnail saved successfully (simulated)'
                });
            }, 100);
        });
    }
    
    // Batch save multiple thumbnails
    async saveBatchThumbnails(thumbnailData) {
        const results = {
            success: 0,
            failed: 0,
            details: []
        };
        
        for (const data of thumbnailData) {
            try {
                const result = await this.saveThumbnailFile(data.video, data.blob, data.filename);
                if (result.success) {
                    results.success++;
                    results.details.push({
                        videoId: data.video.id,
                        status: 'saved',
                        path: result.path,
                        size: result.size
                    });
                } else {
                    results.failed++;
                    results.details.push({
                        videoId: data.video.id,
                        status: 'failed',
                        error: result.error
                    });
                }
            } catch (error) {
                results.failed++;
                results.details.push({
                    videoId: data.video.id,
                    status: 'error',
                    error: error.message
                });
            }
            
            // Small delay between saves
            await this.delay(50);
        }
        
        return results;
    }
    
    // Check if thumbnail file exists on server
    async checkThumbnailExists(thumbnailPath) {
        try {
            const response = await fetch(thumbnailPath, { method: 'HEAD' });
            return response.ok;
        } catch (error) {
            return false;
        }
    }
    
    // Delete thumbnail file from server
    async deleteThumbnailFile(thumbnailPath) {
        try {
            // In a real implementation, this would call a delete endpoint
            const response = await fetch('/api/delete-thumbnail', {
                method: 'DELETE',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ path: thumbnailPath })
            });
            
            return response.ok;
        } catch (error) {
            console.error('Error deleting thumbnail:', error);
            return false;
        }
    }
    
    // Get thumbnail file info
    async getThumbnailInfo(thumbnailPath) {
        try {
            const response = await fetch('/api/thumbnail-info', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ path: thumbnailPath })
            });
            
            if (response.ok) {
                return await response.json();
            }
            
            return null;
        } catch (error) {
            console.error('Error getting thumbnail info:', error);
            return null;
        }
    }
    
    // Clean up old/unused thumbnails
    async cleanupOldThumbnails(activeVideoIds) {
        try {
            const response = await fetch('/api/cleanup-thumbnails', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ activeVideoIds: activeVideoIds })
            });
            
            if (response.ok) {
                const result = await response.json();
                console.log('Thumbnail cleanup result:', result);
                return result;
            }
            
            return { success: false, error: 'Cleanup request failed' };
        } catch (error) {
            console.error('Error during thumbnail cleanup:', error);
            return { success: false, error: error.message };
        }
    }
    
    // Convert blob to base64 (alternative save method)
    async blobToBase64(blob) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = () => resolve(reader.result);
            reader.onerror = reject;
            reader.readAsDataURL(blob);
        });
    }
    
    // Save thumbnail as base64 (fallback method)
    async saveThumbnailAsBase64(video, blob, filename = null) {
        try {
            const base64Data = await this.blobToBase64(blob);
            
            if (!filename) {
                const videoFileName = video.videoUrl.split('/').pop();
                filename = videoFileName.replace(/\.(ts|mp4|webm|avi|mov|mkv|flv)$/i, '.jpg');
            }
            
            const response = await fetch('/api/save-thumbnail-base64', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    videoId: video.id,
                    filename: filename,
                    data: base64Data,
                    folder: 'Thumbnails'
                })
            });
            
            if (response.ok) {
                const result = await response.json();
                return {
                    success: true,
                    path: result.path,
                    url: result.url
                };
            } else {
                throw new Error('Server error saving base64 thumbnail');
            }
            
        } catch (error) {
            console.error('Error saving base64 thumbnail:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }
    
    // Generate unique filename to avoid conflicts
    generateUniqueFilename(originalFilename) {
        const timestamp = Date.now();
        const random = Math.random().toString(36).substring(2, 8);
        const extension = originalFilename.split('.').pop();
        const baseName = originalFilename.replace(/\.[^/.]+$/, '');
        
        return `${baseName}_${timestamp}_${random}.${extension}`;
    }
    
    // Utility method
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
    
    // Get persistence statistics
    getStats() {
        return {
            endpoint: this.serverEndpoint,
            folder: this.thumbnailsFolder,
            maxFileSize: this.maxFileSize,
            allowedTypes: this.allowedTypes
        };
    }
}

// Export for use
window.ThumbnailPersistence = ThumbnailPersistence;
