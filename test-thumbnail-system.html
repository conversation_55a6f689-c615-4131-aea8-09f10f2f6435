<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Thumbnail System Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            margin: 20px 0;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-button {
            background: #007cba;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 4px;
            cursor: pointer;
        }
        .test-button:hover {
            background: #005a87;
        }
        .test-results {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .photo-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .photo-card {
            border: 1px solid #ddd;
            border-radius: 8px;
            overflow: hidden;
            background: white;
        }
        .photo-card img {
            width: 100%;
            height: 150px;
            object-fit: cover;
        }
        .photo-card .info {
            padding: 10px;
        }
        .video-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .video-card {
            border: 1px solid #ddd;
            border-radius: 8px;
            overflow: hidden;
            background: white;
        }
        .video-card img {
            width: 100%;
            height: 200px;
            object-fit: cover;
        }
        .video-card .info {
            padding: 10px;
        }
        .status-indicator {
            display: inline-block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-right: 5px;
        }
        .status-success { background-color: #28a745; }
        .status-error { background-color: #dc3545; }
        .status-warning { background-color: #ffc107; }
    </style>
</head>
<body>
    <h1>🎬 Thumbnail System Test Page</h1>
    
    <div class="test-section">
        <h2>System Status</h2>
        <button class="test-button" onclick="checkSystemStatus()">Check System Status</button>
        <button class="test-button" onclick="runComprehensiveReport()">Run Comprehensive Report</button>
        <div id="system-status" class="test-results"></div>
    </div>
    
    <div class="test-section">
        <h2>Photo Display Test</h2>
        <p>Testing if photos display correctly instead of showing fallback images:</p>
        <button class="test-button" onclick="testPhotoDisplay()">Test Photo Display</button>
        <div id="photo-test-results" class="test-results"></div>
        <div id="photo-display" class="photo-grid"></div>
    </div>
    
    <div class="test-section">
        <h2>Video Thumbnail Generation Test</h2>
        <p>Testing video thumbnail generation for videos that need thumbnails:</p>
        <button class="test-button" onclick="testVideoThumbnails()">Test Video Thumbnails</button>
        <button class="test-button" onclick="generateMissingThumbnails()">Generate Missing Thumbnails</button>
        <div id="video-test-results" class="test-results"></div>
        <div id="video-display" class="video-grid"></div>
    </div>
    
    <div class="test-section">
        <h2>Console Commands</h2>
        <p>Open browser console and try these commands:</p>
        <ul>
            <li><code>comprehensiveReport()</code> - Get detailed thumbnail report</li>
            <li><code>comprehensiveStats()</code> - Get system statistics</li>
            <li><code>generateAllThumbnails()</code> - Generate missing thumbnails</li>
            <li><code>regenerateAllThumbnails()</code> - Force regenerate all thumbnails</li>
        </ul>
    </div>

    <!-- Include all the necessary scripts -->
    <script src="assets/js/thumbnail-generator.js"></script>
    <script src="assets/js/comprehensive-thumbnail-generator.js"></script>
    <script src="assets/js/thumbnail-manager.js"></script>
    <script src="assets/js/thumbnail-utils.js"></script>
    <script src="assets/js/enhanced-thumbnail-system.js"></script>
    <script src="assets/js/thumbnail-persistence.js"></script>
    <script src="assets/js/comprehensive-thumbnail-manager.js"></script>
    <script src="assets/js/main.js"></script>

    <script>
        // Wait for app to initialize
        let app = null;
        
        function waitForApp() {
            if (window.app && window.app.videos && window.app.videos.length > 0) {
                app = window.app;
                console.log('✅ App initialized with', app.videos.length, 'videos');
                checkSystemStatus();
            } else {
                setTimeout(waitForApp, 500);
            }
        }
        
        // Start waiting for app
        setTimeout(waitForApp, 1000);
        
        function checkSystemStatus() {
            const statusDiv = document.getElementById('system-status');
            
            if (!app) {
                statusDiv.textContent = '❌ App not initialized yet. Please wait...';
                return;
            }
            
            const stats = app.comprehensiveThumbnailManager ? 
                app.comprehensiveThumbnailManager.getStats() : 
                { error: 'Comprehensive thumbnail manager not available' };
            
            statusDiv.textContent = `System Status:
📹 Total Videos: ${stats.totalVideos || 'N/A'}
✅ Videos with Thumbnails: ${stats.videosWithThumbnails || 'N/A'}
❌ Videos Needing Thumbnails: ${stats.videosNeedingThumbnails || 'N/A'}
🆕 Generated This Session: ${stats.generatedThisSession || 'N/A'}
⚙️ Currently Generating: ${stats.isGenerating ? 'Yes' : 'No'}

📸 Total Photos: ${app.newPhotos ? app.newPhotos.length : 'N/A'}

System Components:
- Enhanced Thumbnail System: ${window.EnhancedThumbnailSystem ? '✅' : '❌'}
- Thumbnail Persistence: ${window.ThumbnailPersistence ? '✅' : '❌'}
- Comprehensive Manager: ${window.ComprehensiveThumbnailManager ? '✅' : '❌'}`;
        }
        
        function runComprehensiveReport() {
            if (app && app.comprehensiveThumbnailManager) {
                app.comprehensiveThumbnailManager.printReport();
                document.getElementById('system-status').textContent += '\n\n📊 Detailed report printed to console.';
            } else {
                document.getElementById('system-status').textContent += '\n\n❌ Comprehensive manager not available.';
            }
        }
        
        function testPhotoDisplay() {
            const resultsDiv = document.getElementById('photo-test-results');
            const displayDiv = document.getElementById('photo-display');
            
            if (!app || !app.newPhotos) {
                resultsDiv.textContent = '❌ No photos available for testing';
                return;
            }
            
            resultsDiv.textContent = `Testing ${app.newPhotos.length} photos...`;
            displayDiv.innerHTML = '';
            
            let successCount = 0;
            let errorCount = 0;
            
            app.newPhotos.forEach((photo, index) => {
                const photoCard = document.createElement('div');
                photoCard.className = 'photo-card';
                
                const img = document.createElement('img');
                img.src = photo.thumbnail || photo.url || photo.videoUrl;
                img.alt = photo.title;
                
                img.onload = () => {
                    successCount++;
                    updatePhotoTestResults();
                };
                
                img.onerror = () => {
                    errorCount++;
                    img.src = '/file.svg'; // Fallback
                    updatePhotoTestResults();
                };
                
                const info = document.createElement('div');
                info.className = 'info';
                info.innerHTML = `
                    <strong>${photo.title}</strong><br>
                    <small>URL: ${photo.thumbnail || photo.url || photo.videoUrl}</small>
                `;
                
                photoCard.appendChild(img);
                photoCard.appendChild(info);
                displayDiv.appendChild(photoCard);
            });
            
            function updatePhotoTestResults() {
                resultsDiv.textContent = `Photo Display Test Results:
✅ Successfully Loaded: ${successCount}
❌ Failed to Load: ${errorCount}
⏳ Still Loading: ${app.newPhotos.length - successCount - errorCount}`;
            }
        }
        
        function testVideoThumbnails() {
            const resultsDiv = document.getElementById('video-test-results');
            const displayDiv = document.getElementById('video-display');
            
            if (!app || !app.videos) {
                resultsDiv.textContent = '❌ No videos available for testing';
                return;
            }
            
            const videos = app.videos.filter(v => v.type !== 'photo').slice(0, 10); // Test first 10 videos
            resultsDiv.textContent = `Testing ${videos.length} videos...`;
            displayDiv.innerHTML = '';
            
            videos.forEach(video => {
                const videoCard = document.createElement('div');
                videoCard.className = 'video-card';
                
                const img = document.createElement('img');
                img.src = video.thumbnail || '/file.svg';
                img.alt = video.title;
                
                const statusIndicator = document.createElement('span');
                statusIndicator.className = 'status-indicator';
                
                if (video.thumbnail && video.thumbnail !== '/file.svg') {
                    statusIndicator.className += ' status-success';
                } else {
                    statusIndicator.className += ' status-warning';
                }
                
                const info = document.createElement('div');
                info.className = 'info';
                info.innerHTML = `
                    ${statusIndicator.outerHTML}
                    <strong>${video.title}</strong><br>
                    <small>Thumbnail: ${video.thumbnail || 'None'}</small><br>
                    <small>Duration: ${video.duration}</small>
                `;
                
                videoCard.appendChild(img);
                videoCard.appendChild(info);
                displayDiv.appendChild(videoCard);
            });
            
            const withThumbnails = videos.filter(v => v.thumbnail && v.thumbnail !== '/file.svg').length;
            const needingThumbnails = videos.length - withThumbnails;
            
            resultsDiv.textContent = `Video Thumbnail Test Results:
✅ Videos with Thumbnails: ${withThumbnails}
❌ Videos Needing Thumbnails: ${needingThumbnails}
📊 Total Tested: ${videos.length}`;
        }
        
        function generateMissingThumbnails() {
            if (app && app.comprehensiveThumbnailManager) {
                document.getElementById('video-test-results').textContent += '\n\n🎬 Starting thumbnail generation...';
                app.comprehensiveThumbnailManager.autoGenerateThumbnails()
                    .then(() => {
                        document.getElementById('video-test-results').textContent += '\n✅ Thumbnail generation completed!';
                        setTimeout(testVideoThumbnails, 1000); // Refresh the display
                    })
                    .catch(error => {
                        document.getElementById('video-test-results').textContent += '\n❌ Error: ' + error.message;
                    });
            } else {
                document.getElementById('video-test-results').textContent += '\n❌ Comprehensive thumbnail manager not available';
            }
        }
    </script>
</body>
</html>
