// Enhanced Thumbnail Generation System
// This system generates thumbnails for videos and saves them to the Thumbnails folder
class EnhancedThumbnailSystem {
    constructor() {
        this.canvas = document.createElement('canvas');
        this.ctx = this.canvas.getContext('2d');
        this.canvas.width = 300;
        this.canvas.height = 200;
        
        // Configuration
        this.config = {
            thumbnailWidth: 300,
            thumbnailHeight: 200,
            captureTimeOffset: 5, // seconds
            quality: 0.8,
            maxRetries: 3,
            timeout: 30000, // 30 seconds
            saveToFolder: true // Enable saving to Thumbnails folder
        };
        
        this.supportedFormats = ['.mp4', '.ts', '.webm', '.avi', '.mov', '.mkv', '.flv'];
        this.generatedThumbnails = new Map(); // Track generated thumbnails
        this.processingQueue = new Set();
    }
    
    // Main method to generate thumbnails for all videos
    async generateAllVideoThumbnails(videos) {
        console.log('🎬 Starting enhanced thumbnail generation for', videos.length, 'videos');
        
        const results = {
            success: 0,
            failed: 0,
            skipped: 0,
            details: []
        };
        
        // Filter only videos (not photos)
        const videoList = videos.filter(video => video.type !== 'photo');
        
        for (const video of videoList) {
            try {
                const result = await this.generateThumbnailForVideo(video);
                if (result.success) {
                    results.success++;
                    results.details.push({
                        videoId: video.id,
                        title: video.title,
                        status: 'success',
                        thumbnailPath: result.thumbnailPath,
                        thumbnailUrl: result.thumbnailUrl
                    });
                } else {
                    results.failed++;
                    results.details.push({
                        videoId: video.id,
                        title: video.title,
                        status: 'failed',
                        error: result.error
                    });
                }
            } catch (error) {
                results.failed++;
                results.details.push({
                    videoId: video.id,
                    title: video.title,
                    status: 'error',
                    error: error.message
                });
            }
            
            // Add small delay to prevent overwhelming the browser
            await this.delay(200);
        }
        
        console.log('✅ Enhanced thumbnail generation complete:', results);
        return results;
    }
    
    // Generate thumbnail for a single video
    async generateThumbnailForVideo(video) {
        // Skip if already processing
        if (this.processingQueue.has(video.id)) {
            return { success: false, error: 'Already processing' };
        }
        
        // Check if video already has a proper thumbnail
        if (video.thumbnail && video.thumbnail !== '/file.svg') {
            try {
                const exists = await this.checkThumbnailExists(video.thumbnail);
                if (exists) {
                    return { success: true, thumbnailPath: video.thumbnail, thumbnailUrl: video.thumbnail };
                }
            } catch (error) {
                console.warn('Error checking existing thumbnail:', error);
            }
        }
        
        this.processingQueue.add(video.id);
        
        try {
            // Generate thumbnail from video
            const result = await this.extractVideoFrame(video);
            
            if (result.success) {
                // Save thumbnail to Thumbnails folder (simulated)
                const thumbnailPath = await this.saveThumbnailToFolder(video, result.blob);
                
                // Update video data
                video.thumbnail = thumbnailPath;
                
                this.generatedThumbnails.set(video.id, {
                    path: thumbnailPath,
                    url: result.blobUrl,
                    timestamp: Date.now()
                });
                
                return { 
                    success: true, 
                    thumbnailPath: thumbnailPath,
                    thumbnailUrl: result.blobUrl
                };
            } else {
                // Fallback to placeholder
                const placeholderResult = await this.generatePlaceholderThumbnail(video);
                const thumbnailPath = await this.saveThumbnailToFolder(video, placeholderResult.blob);
                
                video.thumbnail = thumbnailPath;
                
                return { 
                    success: true, 
                    thumbnailPath: thumbnailPath,
                    thumbnailUrl: placeholderResult.blobUrl,
                    fallback: true
                };
            }
        } catch (error) {
            console.warn(`Failed to generate thumbnail for ${video.title}:`, error);
            return { success: false, error: error.message };
        } finally {
            this.processingQueue.delete(video.id);
        }
    }
    
    // Extract frame from video file
    async extractVideoFrame(video) {
        return new Promise((resolve) => {
            const videoElement = document.createElement('video');
            videoElement.crossOrigin = 'anonymous';
            videoElement.muted = true;
            videoElement.preload = 'metadata';
            
            let timeoutId;
            let hasResolved = false;
            
            const cleanup = () => {
                if (timeoutId) clearTimeout(timeoutId);
                videoElement.removeEventListener('loadedmetadata', onLoadedMetadata);
                videoElement.removeEventListener('seeked', onSeeked);
                videoElement.removeEventListener('error', onError);
                videoElement.src = '';
            };
            
            const resolveOnce = (result) => {
                if (!hasResolved) {
                    hasResolved = true;
                    cleanup();
                    resolve(result);
                }
            };
            
            const onLoadedMetadata = () => {
                try {
                    // Calculate capture time (5 seconds or 10% of duration, whichever is smaller)
                    const captureTime = Math.min(
                        this.config.captureTimeOffset,
                        videoElement.duration * 0.1,
                        videoElement.duration - 1
                    );
                    
                    videoElement.currentTime = Math.max(0, captureTime);
                } catch (error) {
                    resolveOnce({ success: false, error: 'Failed to set video time: ' + error.message });
                }
            };
            
            const onSeeked = () => {
                try {
                    // Draw video frame to canvas
                    this.ctx.drawImage(videoElement, 0, 0, this.config.thumbnailWidth, this.config.thumbnailHeight);
                    
                    // Convert canvas to blob
                    this.canvas.toBlob((blob) => {
                        if (blob) {
                            const blobUrl = URL.createObjectURL(blob);
                            resolveOnce({ success: true, blob: blob, blobUrl: blobUrl });
                        } else {
                            resolveOnce({ success: false, error: 'Failed to create thumbnail blob' });
                        }
                    }, 'image/jpeg', this.config.quality);
                } catch (error) {
                    resolveOnce({ success: false, error: 'Failed to draw video frame: ' + error.message });
                }
            };
            
            const onError = (event) => {
                const error = videoElement.error;
                let errorMessage = 'Unknown video error';
                
                if (error) {
                    switch (error.code) {
                        case error.MEDIA_ERR_ABORTED:
                            errorMessage = 'Video loading aborted';
                            break;
                        case error.MEDIA_ERR_NETWORK:
                            errorMessage = 'Network error while loading video';
                            break;
                        case error.MEDIA_ERR_DECODE:
                            errorMessage = 'Video decode error';
                            break;
                        case error.MEDIA_ERR_SRC_NOT_SUPPORTED:
                            errorMessage = 'Video format not supported';
                            break;
                    }
                }
                
                resolveOnce({ success: false, error: errorMessage });
            };
            
            // Set up event listeners
            videoElement.addEventListener('loadedmetadata', onLoadedMetadata);
            videoElement.addEventListener('seeked', onSeeked);
            videoElement.addEventListener('error', onError);
            
            // Set timeout
            timeoutId = setTimeout(() => {
                resolveOnce({ success: false, error: 'Video loading timeout' });
            }, this.config.timeout);
            
            // Start loading video
            videoElement.src = video.videoUrl;
        });
    }
    
    // Generate placeholder thumbnail with video info
    async generatePlaceholderThumbnail(video) {
        return new Promise((resolve) => {
            // Clear canvas
            this.ctx.fillStyle = '#2a2a2a';
            this.ctx.fillRect(0, 0, this.config.thumbnailWidth, this.config.thumbnailHeight);
            
            // Add gradient background
            const gradient = this.ctx.createLinearGradient(0, 0, this.config.thumbnailWidth, this.config.thumbnailHeight);
            gradient.addColorStop(0, '#2a2a2a');
            gradient.addColorStop(1, '#1a1a1a');
            this.ctx.fillStyle = gradient;
            this.ctx.fillRect(0, 0, this.config.thumbnailWidth, this.config.thumbnailHeight);
            
            // Add category-specific styling
            const categoryColor = this.getCategoryColor(video.subcategory);
            
            // Add play button
            this.ctx.fillStyle = categoryColor;
            this.ctx.beginPath();
            this.ctx.arc(this.config.thumbnailWidth / 2, this.config.thumbnailHeight / 2, 30, 0, 2 * Math.PI);
            this.ctx.fill();
            
            // Add play triangle
            this.ctx.fillStyle = 'white';
            this.ctx.beginPath();
            this.ctx.moveTo(this.config.thumbnailWidth / 2 - 10, this.config.thumbnailHeight / 2 - 15);
            this.ctx.lineTo(this.config.thumbnailWidth / 2 - 10, this.config.thumbnailHeight / 2 + 15);
            this.ctx.lineTo(this.config.thumbnailWidth / 2 + 15, this.config.thumbnailHeight / 2);
            this.ctx.closePath();
            this.ctx.fill();
            
            // Add video title (truncated)
            this.ctx.fillStyle = '#b3b3b3';
            this.ctx.font = '12px Arial, sans-serif';
            this.ctx.textAlign = 'center';
            const title = video.title.length > 35 ? video.title.substring(0, 35) + '...' : video.title;
            this.ctx.fillText(title, this.config.thumbnailWidth / 2, this.config.thumbnailHeight - 20);
            
            // Add duration
            this.ctx.fillStyle = '#ffffff';
            this.ctx.font = 'bold 10px Arial, sans-serif';
            this.ctx.textAlign = 'right';
            this.ctx.fillText(video.duration, this.config.thumbnailWidth - 10, 20);
            
            // Add category indicator
            this.ctx.fillStyle = categoryColor;
            this.ctx.font = 'bold 8px Arial, sans-serif';
            this.ctx.textAlign = 'left';
            this.ctx.fillText(video.subcategory?.toUpperCase() || 'VIDEO', 10, 20);
            
            // Convert to blob
            this.canvas.toBlob((blob) => {
                const blobUrl = URL.createObjectURL(blob);
                resolve({ blob: blob, blobUrl: blobUrl });
            }, 'image/jpeg', this.config.quality);
        });
    }
    
    // Get category-specific color
    getCategoryColor(subcategory) {
        const colors = {
            'threesome': '#ff6b6b',
            'lesbian': '#4ecdc4',
            'stepsister': '#45b7d1',
            'family': '#45b7d1',
            'asian': '#ff9800',
            'romantic': '#ff6b6b',
            'general': '#666666'
        };
        
        return colors[subcategory] || colors.general;
    }
    
    // Simulate saving thumbnail to Thumbnails folder
    async saveThumbnailToFolder(video, blob) {
        // In a real implementation, this would save the blob to the server
        // For now, we'll generate the expected path and log the action
        
        const videoFileName = video.videoUrl.split('/').pop();
        const thumbnailName = videoFileName.replace(/\.(ts|mp4|webm|avi|mov|mkv|flv)$/i, '.jpg');
        const thumbnailPath = `/categories/Thumbnails/${thumbnailName}`;
        
        console.log(`📁 Would save thumbnail to: ${thumbnailPath} (${blob.size} bytes)`);
        
        // In a real implementation, you would:
        // 1. Convert blob to FormData
        // 2. Send POST request to server endpoint
        // 3. Server saves file to categories/Thumbnails/ folder
        // 4. Return the saved file path
        
        return thumbnailPath;
    }
    
    // Check if thumbnail exists
    async checkThumbnailExists(thumbnailUrl) {
        try {
            const response = await fetch(thumbnailUrl, { method: 'HEAD' });
            return response.ok;
        } catch (error) {
            return false;
        }
    }
    
    // Update video cards with new thumbnails
    updateVideoCardThumbnails(results) {
        results.details.forEach(detail => {
            if (detail.status === 'success') {
                this.updateVideoCardThumbnail(detail.videoId, detail.thumbnailUrl);
            }
        });
    }
    
    // Update specific video card thumbnail
    updateVideoCardThumbnail(videoId, thumbnailUrl) {
        // Find and update video card thumbnails
        const selectors = [
            `[href*="id=${videoId}"] img`,
            `[onclick*="${videoId}"] img`,
            `img[data-video-id="${videoId}"]`
        ];
        
        selectors.forEach(selector => {
            const images = document.querySelectorAll(selector);
            images.forEach(img => {
                if (img.src.includes('/file.svg') || 
                    img.src.includes('categories/Thumbnails') || 
                    img.src.includes('placeholder')) {
                    img.src = thumbnailUrl;
                    img.onerror = null; // Remove error handler to prevent loops
                    
                    // Remove generating indicator
                    const indicator = img.parentElement.querySelector('.generating-indicator');
                    if (indicator) {
                        indicator.remove();
                    }
                }
            });
        });
    }
    
    // Utility methods
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
    
    // Get generation statistics
    getStats() {
        return {
            generated: this.generatedThumbnails.size,
            processing: this.processingQueue.size,
            config: this.config
        };
    }
    
    // Clean up blob URLs to prevent memory leaks
    cleanup() {
        this.generatedThumbnails.forEach((thumbnail) => {
            if (thumbnail.url && thumbnail.url.startsWith('blob:')) {
                URL.revokeObjectURL(thumbnail.url);
            }
        });
        this.generatedThumbnails.clear();
        this.processingQueue.clear();
    }
}

// Export for use
window.EnhancedThumbnailSystem = EnhancedThumbnailSystem;
