// Thumbnail Utilities - Helper functions for thumbnail management
window.ThumbnailUtils = {
    
    // Get thumbnail generation statistics
    getStats() {
        if (window.app && window.app.thumbnailManager) {
            return window.app.thumbnailManager.getStats();
        }
        return { error: 'Thumbnail manager not available' };
    },
    
    // Manually trigger thumbnail generation for all videos
    async generateAllThumbnails() {
        if (!window.app || !window.app.thumbnailManager) {
            console.error('Thumbnail manager not available');
            return;
        }
        
        console.log('Manually triggering thumbnail generation...');
        await window.app.thumbnailManager.autoGenerateThumbnails();
    },
    
    // Regenerate all thumbnails (clears cache first)
    async regenerateAllThumbnails() {
        if (!window.app || !window.app.thumbnailManager) {
            console.error('Thumbnail manager not available');
            return;
        }
        
        console.log('Regenerating all thumbnails...');
        await window.app.thumbnailManager.regenerateAllThumbnails();
    },
    
    // Generate thumbnail for specific video
    async generateThumbnailForVideo(videoId) {
        if (!window.app || !window.app.thumbnailManager) {
            console.error('Thumbnail manager not available');
            return;
        }
        
        try {
            const result = await window.app.thumbnailManager.generateThumbnailForVideo(videoId);
            console.log(`Thumbnail generation result for ${videoId}:`, result);
            return result;
        } catch (error) {
            console.error(`Failed to generate thumbnail for ${videoId}:`, error);
            return { success: false, error: error.message };
        }
    },
    
    // List videos that need thumbnails
    getVideosNeedingThumbnails() {
        if (!window.app || !window.app.videos) {
            return [];
        }
        
        return window.app.videos.filter(video => 
            video.type !== 'photo' && 
            (video.thumbnail === '/file.svg' || !video.thumbnail)
        );
    },
    
    // List videos with thumbnails
    getVideosWithThumbnails() {
        if (!window.app || !window.app.videos) {
            return [];
        }
        
        return window.app.videos.filter(video => 
            video.type !== 'photo' && 
            video.thumbnail && 
            video.thumbnail !== '/file.svg'
        );
    },
    
    // Check if thumbnail generation is in progress
    isGenerating() {
        if (window.app && window.app.thumbnailManager) {
            return window.app.thumbnailManager.isGenerating;
        }
        return false;
    },
    
    // Get current generation progress
    getProgress() {
        if (window.app && window.app.thumbnailManager) {
            return window.app.thumbnailManager.generationProgress;
        }
        return 0;
    },
    
    // Print detailed report
    printReport() {
        const stats = this.getStats();
        const videosNeedingThumbnails = this.getVideosNeedingThumbnails();
        const videosWithThumbnails = this.getVideosWithThumbnails();
        
        console.group('📊 Thumbnail Generation Report');
        console.log('📈 Statistics:', stats);
        console.log('✅ Videos with thumbnails:', videosWithThumbnails.length);
        console.log('❌ Videos needing thumbnails:', videosNeedingThumbnails.length);
        
        if (videosNeedingThumbnails.length > 0) {
            console.group('📋 Videos needing thumbnails:');
            videosNeedingThumbnails.forEach(video => {
                console.log(`- ${video.id}: ${video.title}`);
            });
            console.groupEnd();
        }
        
        console.groupEnd();
        
        return {
            stats,
            videosWithThumbnails: videosWithThumbnails.length,
            videosNeedingThumbnails: videosNeedingThumbnails.length,
            needingThumbnailsList: videosNeedingThumbnails.map(v => ({ id: v.id, title: v.title }))
        };
    },
    
    // Test thumbnail generation for a single video
    async testThumbnailGeneration(videoId) {
        console.log(`🧪 Testing thumbnail generation for video: ${videoId}`);
        
        const video = window.app?.videos?.find(v => v.id === videoId);
        if (!video) {
            console.error('Video not found');
            return;
        }
        
        console.log('📹 Video details:', {
            id: video.id,
            title: video.title,
            videoUrl: video.videoUrl,
            currentThumbnail: video.thumbnail
        });
        
        try {
            const result = await this.generateThumbnailForVideo(videoId);
            console.log('✅ Test result:', result);
            return result;
        } catch (error) {
            console.error('❌ Test failed:', error);
            return { success: false, error: error.message };
        }
    },
    
    // Cleanup thumbnail cache
    cleanup() {
        if (window.app && window.app.thumbnailManager) {
            window.app.thumbnailManager.destroy();
            console.log('🧹 Thumbnail cache cleaned up');
        }
    }
};

// Console helper functions for easy access
window.thumbnailStats = () => window.ThumbnailUtils.getStats();
window.generateThumbnails = () => window.ThumbnailUtils.generateAllThumbnails();
window.regenerateThumbnails = () => window.ThumbnailUtils.regenerateAllThumbnails();
window.thumbnailReport = () => window.ThumbnailUtils.printReport();

// Enhanced console helpers for comprehensive system
window.comprehensiveStats = () => {
    if (window.app && window.app.comprehensiveThumbnailManager) {
        return window.app.comprehensiveThumbnailManager.getStats();
    }
    return { error: 'Comprehensive thumbnail manager not available' };
};

window.comprehensiveReport = () => {
    if (window.app && window.app.comprehensiveThumbnailManager) {
        return window.app.comprehensiveThumbnailManager.printReport();
    }
    console.error('Comprehensive thumbnail manager not available');
};

window.generateAllThumbnails = () => {
    if (window.app && window.app.comprehensiveThumbnailManager) {
        return window.app.comprehensiveThumbnailManager.autoGenerateThumbnails();
    }
    console.error('Comprehensive thumbnail manager not available');
};

window.regenerateAllThumbnails = () => {
    if (window.app && window.app.comprehensiveThumbnailManager) {
        return window.app.comprehensiveThumbnailManager.regenerateAllThumbnails();
    }
    console.error('Comprehensive thumbnail manager not available');
};

// Auto-run report when utils are loaded (after a delay to ensure app is ready)
setTimeout(() => {
    if (window.app && window.app.videos) {
        console.log('🎬 Enhanced Thumbnail Utils loaded!');
        console.log('📊 Use comprehensiveReport() to see detailed status.');
        console.log('🔧 Available commands:');
        console.log('   - comprehensiveStats() - Get detailed statistics');
        console.log('   - comprehensiveReport() - Print full report');
        console.log('   - generateAllThumbnails() - Generate missing thumbnails');
        console.log('   - regenerateAllThumbnails() - Force regenerate all thumbnails');
        console.log('   - thumbnailStats() - Legacy stats');
        console.log('   - thumbnailReport() - Legacy report');
    }
}, 2000);
