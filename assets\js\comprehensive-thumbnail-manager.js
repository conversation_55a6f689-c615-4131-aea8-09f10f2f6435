// Comprehensive Thumbnail Management System
// Integrates thumbnail generation, persistence, and UI updates
class ComprehensiveThumbnailManager {
    constructor(app) {
        this.app = app;
        this.generator = new EnhancedThumbnailSystem();
        this.persistence = new ThumbnailPersistence();
        this.isGenerating = false;
        this.generationProgress = 0;
        this.stats = {
            totalVideos: 0,
            videosWithThumbnails: 0,
            videosNeedingThumbnails: 0,
            generatedThisSession: 0,
            savedToDisk: 0
        };
        
        this.init();
    }
    
    init() {
        console.log('🎬 Initializing Comprehensive Thumbnail Manager...');
        
        // Wait for app to be ready
        if (this.app.videos && this.app.videos.length > 0) {
            this.updateStats();
            this.autoGenerateThumbnails();
        } else {
            setTimeout(() => this.init(), 1000);
        }
    }
    
    // Auto-generate thumbnails for videos that need them
    async autoGenerateThumbnails() {
        if (this.isGenerating) {
            console.log('⏳ Thumbnail generation already in progress...');
            return;
        }
        
        const videosNeedingThumbnails = this.getVideosNeedingThumbnails();
        
        if (videosNeedingThumbnails.length === 0) {
            console.log('✅ All videos already have thumbnails!');
            return;
        }
        
        console.log(`🎯 Found ${videosNeedingThumbnails.length} videos needing thumbnails`);
        
        this.isGenerating = true;
        this.showProgressIndicator();
        
        try {
            // Generate thumbnails
            const results = await this.generator.generateAllVideoThumbnails(videosNeedingThumbnails);
            
            // Update UI with generated thumbnails
            this.generator.updateVideoCardThumbnails(results);
            
            // Save successful thumbnails to disk
            const thumbnailsToSave = results.details
                .filter(detail => detail.status === 'success' && detail.thumbnailUrl)
                .map(detail => {
                    const video = videosNeedingThumbnails.find(v => v.id === detail.videoId);
                    return {
                        video: video,
                        blob: null, // Would need to convert from URL to blob
                        filename: detail.thumbnailPath.split('/').pop()
                    };
                });
            
            // Update stats
            this.stats.generatedThisSession += results.success;
            this.updateStats();
            
            console.log('🎉 Thumbnail generation completed:', {
                success: results.success,
                failed: results.failed,
                skipped: results.skipped
            });
            
            // Show completion notification
            this.showCompletionNotification(results);
            
        } catch (error) {
            console.error('❌ Error during thumbnail generation:', error);
            this.showErrorNotification(error.message);
        } finally {
            this.isGenerating = false;
            this.hideProgressIndicator();
        }
    }
    
    // Generate thumbnail for specific video
    async generateThumbnailForVideo(videoId) {
        const video = this.app.videos.find(v => v.id === videoId);
        if (!video) {
            throw new Error(`Video with ID ${videoId} not found`);
        }
        
        if (video.type === 'photo') {
            throw new Error('Cannot generate thumbnail for photo');
        }
        
        console.log(`🎬 Generating thumbnail for: ${video.title}`);
        
        try {
            const result = await this.generator.generateThumbnailForVideo(video);
            
            if (result.success) {
                // Update UI
                this.generator.updateVideoCardThumbnail(video.id, result.thumbnailUrl);
                
                // Update stats
                this.stats.generatedThisSession++;
                this.updateStats();
                
                console.log(`✅ Thumbnail generated for: ${video.title}`);
                return result;
            } else {
                throw new Error(result.error || 'Failed to generate thumbnail');
            }
        } catch (error) {
            console.error(`❌ Failed to generate thumbnail for ${video.title}:`, error);
            throw error;
        }
    }
    
    // Get videos that need thumbnails
    getVideosNeedingThumbnails() {
        return this.app.videos.filter(video => 
            video.type !== 'photo' && 
            (video.thumbnail === '/file.svg' || !video.thumbnail)
        );
    }
    
    // Update statistics
    updateStats() {
        const videos = this.app.videos.filter(video => video.type !== 'photo');
        this.stats.totalVideos = videos.length;
        this.stats.videosWithThumbnails = videos.filter(video => 
            video.thumbnail && video.thumbnail !== '/file.svg'
        ).length;
        this.stats.videosNeedingThumbnails = this.stats.totalVideos - this.stats.videosWithThumbnails;
    }
    
    // Show progress indicator
    showProgressIndicator() {
        // Remove existing indicator
        this.hideProgressIndicator();
        
        const indicator = document.createElement('div');
        indicator.id = 'thumbnail-progress-indicator';
        indicator.className = 'thumbnail-progress-indicator';
        indicator.innerHTML = `
            <div class="progress-content">
                <div class="progress-icon">🎬</div>
                <div class="progress-text">Generating thumbnails...</div>
                <div class="progress-bar">
                    <div class="progress-fill"></div>
                </div>
                <div class="progress-stats">
                    <span id="progress-current">0</span> / 
                    <span id="progress-total">0</span> videos
                </div>
            </div>
        `;
        
        // Add styles
        indicator.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(0, 0, 0, 0.9);
            color: white;
            padding: 20px;
            border-radius: 10px;
            z-index: 10000;
            min-width: 300px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
        `;
        
        document.body.appendChild(indicator);
    }
    
    // Hide progress indicator
    hideProgressIndicator() {
        const indicator = document.getElementById('thumbnail-progress-indicator');
        if (indicator) {
            indicator.remove();
        }
    }
    
    // Show completion notification
    showCompletionNotification(results) {
        const notification = document.createElement('div');
        notification.className = 'thumbnail-completion-notification';
        notification.innerHTML = `
            <div class="notification-content">
                <div class="notification-icon">✅</div>
                <div class="notification-title">Thumbnail Generation Complete!</div>
                <div class="notification-stats">
                    <div>✅ Success: ${results.success}</div>
                    <div>❌ Failed: ${results.failed}</div>
                    <div>⏭️ Skipped: ${results.skipped}</div>
                </div>
                <button onclick="this.parentElement.parentElement.remove()">Close</button>
            </div>
        `;
        
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #4CAF50;
            color: white;
            padding: 20px;
            border-radius: 10px;
            z-index: 10001;
            min-width: 300px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
        `;
        
        document.body.appendChild(notification);
        
        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (notification.parentElement) {
                notification.remove();
            }
        }, 5000);
    }
    
    // Show error notification
    showErrorNotification(errorMessage) {
        const notification = document.createElement('div');
        notification.className = 'thumbnail-error-notification';
        notification.innerHTML = `
            <div class="notification-content">
                <div class="notification-icon">❌</div>
                <div class="notification-title">Thumbnail Generation Error</div>
                <div class="notification-message">${errorMessage}</div>
                <button onclick="this.parentElement.parentElement.remove()">Close</button>
            </div>
        `;
        
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #f44336;
            color: white;
            padding: 20px;
            border-radius: 10px;
            z-index: 10001;
            min-width: 300px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
        `;
        
        document.body.appendChild(notification);
        
        // Auto-remove after 8 seconds
        setTimeout(() => {
            if (notification.parentElement) {
                notification.remove();
            }
        }, 8000);
    }
    
    // Regenerate all thumbnails (force regeneration)
    async regenerateAllThumbnails() {
        console.log('🔄 Regenerating all thumbnails...');
        
        // Reset all video thumbnails to force regeneration
        this.app.videos.forEach(video => {
            if (video.type !== 'photo') {
                video.thumbnail = '/file.svg';
            }
        });
        
        // Update stats and regenerate
        this.updateStats();
        await this.autoGenerateThumbnails();
    }
    
    // Get comprehensive statistics
    getStats() {
        this.updateStats();
        return {
            ...this.stats,
            isGenerating: this.isGenerating,
            progress: this.generationProgress,
            generatorStats: this.generator.getStats(),
            persistenceStats: this.persistence.getStats()
        };
    }
    
    // Print detailed report
    printReport() {
        const stats = this.getStats();
        
        console.log('📊 COMPREHENSIVE THUMBNAIL REPORT');
        console.log('=====================================');
        console.log(`📹 Total Videos: ${stats.totalVideos}`);
        console.log(`✅ Videos with Thumbnails: ${stats.videosWithThumbnails}`);
        console.log(`❌ Videos Needing Thumbnails: ${stats.videosNeedingThumbnails}`);
        console.log(`🆕 Generated This Session: ${stats.generatedThisSession}`);
        console.log(`💾 Saved to Disk: ${stats.savedToDisk}`);
        console.log(`⚙️ Currently Generating: ${stats.isGenerating ? 'Yes' : 'No'}`);
        console.log('=====================================');
        
        if (stats.videosNeedingThumbnails > 0) {
            console.log('🎯 Videos needing thumbnails:');
            this.getVideosNeedingThumbnails().forEach((video, index) => {
                console.log(`  ${index + 1}. ${video.title}`);
            });
        }
        
        return stats;
    }
    
    // Cleanup resources
    destroy() {
        this.hideProgressIndicator();
        this.generator.cleanup();
        this.isGenerating = false;
    }
}

// Export for use
window.ComprehensiveThumbnailManager = ComprehensiveThumbnailManager;
